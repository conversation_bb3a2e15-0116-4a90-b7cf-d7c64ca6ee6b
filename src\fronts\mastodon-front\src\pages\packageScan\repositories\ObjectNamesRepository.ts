import {BaserowRepository} from './BaserowRepository';
import {ObjectNamesEntity, BRAND_OPTIONS} from '../model/ObjectNamesEntity';

/**
 * Repository for ObjectNames table operations in Baserow
 * Table ID: 1733
 * 
 * Provides CRUD operations for object names/categories with proper entity mapping
 */
export class ObjectNamesRepository extends BaserowRepository {
    protected readonly tableId: number = 1733;

    /**
     * List all object names with optional filtering and pagination
     */
    async listObjectNames(options: {
        page?: number;
        size?: number;
        search?: string;
        orderBy?: string;
        brand?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectNamesEntity[] }> {
        const filters: any = {};
        
        // Add brand filter if specified
        if (options.brand !== undefined) {
            filters.filter_type = 'AND';
            filters.filters = [{
                field: 'field_16711', // brand field
                type: 'single_select_equal',
                value: options.brand
            }];
        }

        const listOptions = {
            ...options,
            userFieldNames: false,
            filters: Object.keys(filters).length > 0 ? filters : undefined
        };

        const response = await this.listRows(listOptions);
        
        return {
            ...response,
            results: response.results.map(this.mapToEntity)
        };
    }

    /**
     * Get a specific object name by ID
     */
    async getObjectName(objectNameId: number): Promise<ObjectNamesEntity> {
        const response = await this.getRow(objectNameId, false);
        return this.mapToEntity(response);
    }

    /**
     * Create a new object name
     */
    async createObjectName(objectNameData: {
        code: string;
        name: string;
        brand: number;
    }): Promise<ObjectNamesEntity> {
        const data = {
            field_16710: objectNameData.code,
            field_16709: objectNameData.name,
            field_16711: objectNameData.brand
        };

        const response = await this.createRow(data, false);
        return this.mapToEntity(response);
    }

    /**
     * Update an existing object name
     */
    async updateObjectName(objectNameId: number, objectNameData: Partial<{
        code: string;
        name: string;
        brand: number;
    }>): Promise<ObjectNamesEntity> {
        const data: any = {};
        
        if (objectNameData.code !== undefined) data.field_16710 = objectNameData.code;
        if (objectNameData.name !== undefined) data.field_16709 = objectNameData.name;
        if (objectNameData.brand !== undefined) data.field_16711 = objectNameData.brand;

        const response = await this.updateRow(objectNameId, data, false);
        return this.mapToEntity(response);
    }

    /**
     * Delete an object name
     */
    async deleteObjectName(objectNameId: number): Promise<void> {
        await this.deleteRow(objectNameId);
    }

    /**
     * Search object names by code
     */
    async searchObjectNamesByCode(searchTerm: string, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectNamesEntity[] }> {
        const filters = {
            filter_type: 'AND',
            filters: [{
                field: 'field_16710', // code field
                type: 'contains',
                value: searchTerm
            }]
        };

        const listOptions = {
            ...options,
            userFieldNames: false,
            filters
        };

        const response = await this.listRows(listOptions);
        
        return {
            ...response,
            results: response.results.map(this.mapToEntity)
        };
    }

    /**
     * Search object names by name
     */
    async searchObjectNamesByName(searchTerm: string, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectNamesEntity[] }> {
        const filters = {
            filter_type: 'AND',
            filters: [{
                field: 'field_16709', // name field
                type: 'contains',
                value: searchTerm
            }]
        };

        const listOptions = {
            ...options,
            userFieldNames: false,
            filters
        };

        const response = await this.listRows(listOptions);
        
        return {
            ...response,
            results: response.results.map(this.mapToEntity)
        };
    }

    /**
     * Get object names by brand
     */
    async getObjectNamesByBrand(brand: number, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectNamesEntity[] }> {
        return this.listObjectNames({
            ...options,
            brand
        });
    }

    /**
     * Get object names by brand name (convenience method)
     */
    async getObjectNamesByBrandName(brandName: keyof typeof BRAND_OPTIONS, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectNamesEntity[] }> {
        const brandId = BRAND_OPTIONS[brandName];
        return this.getObjectNamesByBrand(brandId, options);
    }

    /**
     * Get all available brands
     */
    getBrandOptions(): typeof BRAND_OPTIONS {
        return BRAND_OPTIONS;
    }

    /**
     * Get object name by code (exact match)
     */
    async getObjectNameByCode(code: string): Promise<ObjectNamesEntity | null> {
        const filters = {
            filter_type: 'AND',
            filters: [{
                field: 'field_16710', // code field
                type: 'equal',
                value: code
            }]
        };

        const response = await this.listRows({
            userFieldNames: false,
            filters,
            size: 1
        });

        if (response.results.length === 0) {
            return null;
        }

        return this.mapToEntity(response.results[0]);
    }

    /**
     * Check if a code already exists
     */
    async codeExists(code: string): Promise<boolean> {
        const result = await this.getObjectNameByCode(code);
        return result !== null;
    }

    /**
     * Get object names with user-friendly field names
     */
    async listObjectNamesWithUserFields(options: {
        page?: number;
        size?: number;
        search?: string;
        orderBy?: string;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: any[] }> {
        const listOptions = {
            ...options,
            userFieldNames: true
        };

        return await this.listRows(listOptions);
    }

    /**
     * Map Baserow API response to ObjectNamesEntity
     */
    private mapToEntity(data: any): ObjectNamesEntity {
        return new ObjectNamesEntity({
            id: data.id,
            code: data.field_16710 || '',
            name: data.field_16709 || '',
            brand: data.field_16711 || BRAND_OPTIONS.SUNMI
        });
    }
}
