import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>time<PERSON>ield,
    <PERSON>tity,
    EntityClass,
    EntityAsSimpleObject,
    IntegerField,
    StringField
} from "@groupk/horizon2-core";

/**
 * Object entity representing an object in the Baserow database
 * Table ID: 1732
 *
 * Field mappings from Baserow API documentation:
 * - field_16701: code (string)
 * - field_16702: packageId (array, link to table 1731 - packages)
 * - field_16703: objectNames (string)
 * - field_16704: return (boolean)
 * - field_16705: Date de création (created_on, read-only)
 * - field_16706: images (array of files)
 * - field_16707: returnImages (array of files)
 * - field_16708: comment (string)
 */
@EntityClass()
export class ObjectEntity extends Entity {
    @IntegerField() id: number;

    /** field_16701: Object code */
    @StringField() code: string;

    /** field_16702: Package ID array - link to table 1731 (packages) */
    @IntegerField({array: true}) packageId: number[];

    /** field_16703: Object names */
    @StringField() objectNames: string;

    /** field_16704: Return status */
    @BoolField() return: boolean;

    /** field_16705: Creation date - read-only */
    @DatetimeField() creationDate: string;

    /** field_16706: Images array (file objects) */
    @StringField({array: true}) images: string[];

    /** field_16707: Return images array (file objects) */
    @StringField({array: true}) returnImages: string[];

    /** field_16708: Comment */
    @StringField() comment: string;

    constructor({
        id,
        code,
        packageId,
        objectNames,
        return: returnValue,
        creationDate,
        images,
        returnImages,
        comment
    }: EntityAsSimpleObject<ObjectEntity>) {
        super();
        this.id = id;
        this.code = code;
        this.packageId = packageId;
        this.objectNames = objectNames;
        this.return = returnValue;
        this.creationDate = creationDate;
        this.images = images;
        this.returnImages = returnImages;
        this.comment = comment;
    }
}
