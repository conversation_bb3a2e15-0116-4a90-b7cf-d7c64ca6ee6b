import {BaserowRepository} from './BaserowRepository';
import {PackageEntity} from '../model/PackageEntity';

/**
 * Repository for Package table operations in Baserow
 * Table ID: 1731
 * 
 * Provides CRUD operations for packages with proper entity mapping
 */
export class PackageRepository extends BaserowRepository {
    protected readonly tableId: number = 1731;

    /**
     * List all packages with optional filtering and pagination
     */
    async listPackages(options: {
        page?: number;
        size?: number;
        search?: string;
        orderBy?: string;
        validated?: boolean;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: PackageEntity[] }> {
        const filters: any = {};
        
        // Add validated filter if specified
        if (options.validated !== undefined) {
            filters.filter_type = 'AND';
            filters.filters = [{
                field: 'field_16699', // validated field
                type: 'boolean',
                value: options.validated
            }];
        }

        const listOptions = {
            ...options,
            userFieldNames: false,
            filters: Object.keys(filters).length > 0 ? filters : undefined
        };

        const response = await this.listRows(listOptions);
        
        return {
            ...response,
            results: response.results.map(this.mapToEntity)
        };
    }

    /**
     * Get a specific package by ID
     */
    async getPackage(packageId: number): Promise<PackageEntity> {
        const response = await this.getRow(packageId, false);
        return this.mapToEntity(response);
    }

    /**
     * Create a new package
     */
    async createPackage(packageData: {
        name: string;
        shipping: string;
        return: string;
        validated?: boolean;
        objects?: number[];
        comment?: string;
    }): Promise<PackageEntity> {
        const data = {
            field_16695: packageData.name,
            field_16696: packageData.shipping,
            field_16698: packageData.return,
            field_16699: packageData.validated || false,
            field_16697: packageData.objects || [],
            field_16700: packageData.comment || ''
        };

        const response = await this.createRow(data, false);
        return this.mapToEntity(response);
    }

    /**
     * Update an existing package
     */
    async updatePackage(packageId: number, packageData: Partial<{
        name: string;
        shipping: string;
        return: string;
        validated: boolean;
        objects: number[];
        comment: string;
    }>): Promise<PackageEntity> {
        const data: any = {};
        
        if (packageData.name !== undefined) data.field_16695 = packageData.name;
        if (packageData.shipping !== undefined) data.field_16696 = packageData.shipping;
        if (packageData.return !== undefined) data.field_16698 = packageData.return;
        if (packageData.validated !== undefined) data.field_16699 = packageData.validated;
        if (packageData.objects !== undefined) data.field_16697 = packageData.objects;
        if (packageData.comment !== undefined) data.field_16700 = packageData.comment;

        const response = await this.updateRow(packageId, data, false);
        return this.mapToEntity(response);
    }

    /**
     * Delete a package
     */
    async deletePackage(packageId: number): Promise<void> {
        await this.deleteRow(packageId);
    }

    /**
     * Search packages by name
     */
    async searchPackagesByName(searchTerm: string, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: PackageEntity[] }> {
        const filters = {
            filter_type: 'AND',
            filters: [{
                field: 'field_16695', // name field
                type: 'contains',
                value: searchTerm
            }]
        };

        const listOptions = {
            ...options,
            userFieldNames: false,
            filters
        };

        const response = await this.listRows(listOptions);
        
        return {
            ...response,
            results: response.results.map(this.mapToEntity)
        };
    }

    /**
     * Get packages by validation status
     */
    async getPackagesByValidationStatus(validated: boolean, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: PackageEntity[] }> {
        return this.listPackages({
            ...options,
            validated
        });
    }

    /**
     * Add objects to a package
     */
    async addObjectsToPackage(packageId: number, objectIds: number[]): Promise<PackageEntity> {
        const currentPackage = await this.getPackage(packageId);
        const updatedObjects = [...new Set([...currentPackage.objects, ...objectIds])];
        
        return this.updatePackage(packageId, { objects: updatedObjects });
    }

    /**
     * Remove objects from a package
     */
    async removeObjectsFromPackage(packageId: number, objectIds: number[]): Promise<PackageEntity> {
        const currentPackage = await this.getPackage(packageId);
        const updatedObjects = currentPackage.objects.filter(id => !objectIds.includes(id));
        
        return this.updatePackage(packageId, { objects: updatedObjects });
    }

    /**
     * Map Baserow API response to PackageEntity
     */
    private mapToEntity(data: any): PackageEntity {
        return new PackageEntity({
            id: data.id,
            name: data.field_16695 || '',
            creationDate: data.field_16694 || '',
            shipping: data.field_16696 || '',
            return: data.field_16698 || '',
            validated: data.field_16699 || false,
            objects: data.field_16697 || [],
            comment: data.field_16700 || ''
        });
    }
}
