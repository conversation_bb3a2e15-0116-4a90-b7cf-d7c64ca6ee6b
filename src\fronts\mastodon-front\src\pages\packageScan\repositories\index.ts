/**
 * Baserow Repository Classes for Package Scanning
 * 
 * This module exports repository classes that provide CRUD operations
 * for the Baserow database tables used in package scanning functionality.
 * 
 * Database: 🔒Colis (ID: 446)
 * API Base URL: https://baserow-api.lab.weecop.fr
 * 
 * Repositories:
 * - BaserowRepository - Base class with common Baserow API functionality
 * - PackageRepository - Operations for Package table (ID: 1731)
 * - ObjectRepository - Operations for Objects table (ID: 1732)
 * - ObjectNamesRepository - Operations for ObjectNames table (ID: 1733)
 * 
 * Features:
 * - Full CRUD operations (Create, Read, Update, Delete)
 * - Advanced filtering and search capabilities
 * - Pagination support
 * - Entity mapping from API responses
 * - Type-safe operations with TypeScript
 * - Error handling and validation
 * 
 * Usage Example:
 * ```typescript
 * import { PackageRepository, ObjectRepository } from './repositories';
 * 
 * const packageRepo = new PackageRepository();
 * const packages = await packageRepo.listPackages({ validated: true });
 * 
 * const objectRepo = new ObjectRepository();
 * const objects = await objectRepo.getObjectsByPackageId(packageId);
 * ```
 */

// Export base repository
export { BaserowRepository } from './BaserowRepository';

// Export specific table repositories
export { PackageRepository } from './PackageRepository';
export { ObjectRepository } from './ObjectRepository';
export { ObjectNamesRepository } from './ObjectNamesRepository';

// Type definitions for easier usage
export type {
    PackageEntity,
    ObjectEntity,
    ObjectNamesEntity,
    BRAND_OPTIONS,
    BRAND_LABELS
} from '../model';

/**
 * Repository factory for creating repository instances
 * Useful for dependency injection or centralized repository management
 */
export class RepositoryFactory {
    private static packageRepository: PackageRepository | null = null;
    private static objectRepository: ObjectRepository | null = null;
    private static objectNamesRepository: ObjectNamesRepository | null = null;

    /**
     * Get or create PackageRepository instance (singleton)
     */
    static getPackageRepository(): PackageRepository {
        if (!this.packageRepository) {
            this.packageRepository = new PackageRepository();
        }
        return this.packageRepository;
    }

    /**
     * Get or create ObjectRepository instance (singleton)
     */
    static getObjectRepository(): ObjectRepository {
        if (!this.objectRepository) {
            this.objectRepository = new ObjectRepository();
        }
        return this.objectRepository;
    }

    /**
     * Get or create ObjectNamesRepository instance (singleton)
     */
    static getObjectNamesRepository(): ObjectNamesRepository {
        if (!this.objectNamesRepository) {
            this.objectNamesRepository = new ObjectNamesRepository();
        }
        return this.objectNamesRepository;
    }

    /**
     * Reset all repository instances (useful for testing)
     */
    static reset(): void {
        this.packageRepository = null;
        this.objectRepository = null;
        this.objectNamesRepository = null;
    }
}

/**
 * Convenience exports for common operations
 */
export const createPackageRepository = () => new PackageRepository();
export const createObjectRepository = () => new ObjectRepository();
export const createObjectNamesRepository = () => new ObjectNamesRepository();
