# Baserow Repository Classes for Package Scanning

This directory contains repository classes that provide a clean abstraction layer for interacting with the Baserow API for package scanning functionality.

## Overview

The repository classes replace direct API calls with structured, type-safe operations for the three main Baserow tables:

- **Package** (Table ID: 1731) - Main package information
- **Objects** (Table ID: 1732) - Objects within packages  
- **ObjectNames** (Table ID: 1733) - Object naming/categorization

## Architecture

### BaserowRepository
Base class providing common functionality for all Baserow table operations:
- HTTP request handling with proper authentication
- Standard CRUD operations (Create, Read, Update, Delete)
- Pagination and filtering support
- Error handling and response mapping

### Specific Repositories

#### PackageRepository
Handles operations for the Package table:
- List packages with filtering (validated status, search, etc.)
- Create, update, delete packages
- Manage package-object relationships
- Search packages by name

#### ObjectRepository  
Handles operations for the Objects table:
- List objects with filtering (package ID, return status, etc.)
- Create, update, delete objects
- Manage object images (regular and return images)
- Search objects by code

#### ObjectNamesRepository
Handles operations for the ObjectNames table:
- List object names with brand filtering
- Create, update, delete object names
- Search by code or name
- Brand management utilities

## Usage Examples

### Basic Operations

```typescript
import { PackageRepository, ObjectRepository, ObjectNamesRepository } from './repositories';

// Create repository instances
const packageRepo = new PackageRepository();
const objectRepo = new ObjectRepository();
const objectNamesRepo = new ObjectNamesRepository();

// List all packages
const packages = await packageRepo.listPackages();

// Get validated packages only
const validatedPackages = await packageRepo.listPackages({ validated: true });

// Create a new package
const newPackage = await packageRepo.createPackage({
    name: 'Package 001',
    shipping: 'SHIP123',
    return: 'RET456',
    comment: 'Test package'
});

// Get objects for a specific package
const objects = await objectRepo.getObjectsByPackageId(packageId);

// Search object names by brand
const sunmiObjects = await objectNamesRepo.getObjectNamesByBrandName('SUNMI');
```

### Advanced Filtering

```typescript
// Search packages by name
const searchResults = await packageRepo.searchPackagesByName('urgent');

// Get objects that need to be returned
const objectsToReturn = await objectRepo.getObjectsByReturnStatus(true);

// Find object name by exact code
const objectName = await objectNamesRepo.getObjectNameByCode('DEV001');
```

### Repository Factory Pattern

```typescript
import { RepositoryFactory } from './repositories';

// Get singleton instances
const packageRepo = RepositoryFactory.getPackageRepository();
const objectRepo = RepositoryFactory.getObjectRepository();
const objectNamesRepo = RepositoryFactory.getObjectNamesRepository();
```

## API Endpoints Covered

### Package Table (1731)
- `GET /api/database/rows/table/1731/` - List packages
- `GET /api/database/rows/table/1731/{id}/` - Get package
- `POST /api/database/rows/table/1731/` - Create package
- `PATCH /api/database/rows/table/1731/{id}/` - Update package
- `DELETE /api/database/rows/table/1731/{id}/` - Delete package
- `PATCH /api/database/rows/table/1731/{id}/move/` - Move package

### Objects Table (1732)
- `GET /api/database/rows/table/1732/` - List objects
- `GET /api/database/rows/table/1732/{id}/` - Get object
- `POST /api/database/rows/table/1732/` - Create object
- `PATCH /api/database/rows/table/1732/{id}/` - Update object
- `DELETE /api/database/rows/table/1732/{id}/` - Delete object
- `PATCH /api/database/rows/table/1732/{id}/move/` - Move object

### ObjectNames Table (1733)
- `GET /api/database/rows/table/1733/` - List object names
- `GET /api/database/rows/table/1733/{id}/` - Get object name
- `POST /api/database/rows/table/1733/` - Create object name
- `PATCH /api/database/rows/table/1733/{id}/` - Update object name
- `DELETE /api/database/rows/table/1733/{id}/` - Delete object name
- `PATCH /api/database/rows/table/1733/{id}/move/` - Move object name

### Fields API
- `GET /api/database/fields/table/{table_id}/` - List table fields

## Field Mappings

### Package Table Fields
- `field_16693` → `id` (autonumber, primary key, read-only)
- `field_16695` → `name` (string)
- `field_16694` → `creationDate` (created_on, read-only)
- `field_16696` → `shipping` (string)
- `field_16698` → `return` (string)
- `field_16699` → `validated` (boolean)
- `field_16697` → `objects` (array, link to table 1732)
- `field_16700` → `comment` (string)

### Objects Table Fields
- `field_16701` → `code` (string)
- `field_16702` → `packageId` (array, link to table 1731)
- `field_16703` → `objectNames` (string)
- `field_16704` → `return` (boolean)
- `field_16705` → `creationDate` (created_on, read-only)
- `field_16706` → `images` (array of files)
- `field_16707` → `returnImages` (array of files)
- `field_16708` → `comment` (string)

### ObjectNames Table Fields
- `field_16710` → `code` (string)
- `field_16709` → `name` (string)
- `field_16711` → `brand` (single select: 7485=Sunmi, 7486=Jonkuu, 7487=Quilive, 7488=PAX)

## Error Handling

All repository methods throw descriptive errors for:
- Network failures
- Authentication issues
- Invalid data
- Not found resources
- Server errors

```typescript
try {
    const package = await packageRepo.getPackage(123);
} catch (error) {
    console.error('Failed to fetch package:', error.message);
}
```

## Configuration

The repositories use the following configuration:
- **Base URL**: `https://baserow-api.lab.weecop.fr`
- **Database ID**: `446`
- **Authentication**: Token-based (`fCK4Uh9UE8bo8KKRM8dHPvReYiQuf7wL`)

## Migration from Direct API Calls

To migrate existing code from direct `genericFetch` calls to repositories:

### Before
```typescript
const response = await genericFetch({
    url: 'https://baserow-api.lab.weecop.fr/api/database/rows/table/1731/',
    method: 'GET',
    headers: { 'Authorization': 'Token fCK4Uh9UE8bo8KKRM8dHPvReYiQuf7wL' }
});
const packages = response.results.map(data => new PackageEntity(data));
```

### After
```typescript
const packageRepo = new PackageRepository();
const packages = await packageRepo.listPackages();
```

## Testing

The repositories can be easily mocked for testing:

```typescript
// Mock repository for testing
class MockPackageRepository extends PackageRepository {
    async listPackages() {
        return { count: 1, next: null, previous: null, results: [mockPackage] };
    }
}
```
