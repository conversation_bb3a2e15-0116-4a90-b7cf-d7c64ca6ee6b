import {
    <PERSON>ol<PERSON>ield,
    DatetimeField,
    Entity,
    EntityClass,
    EntityAsSimpleObject,
    EntityField,
    IntegerField,
    StringField
} from "@groupk/horizon2-core";

/**
 * Object Package ID reference for linking to Package table
 */
@EntityClass()
export class ObjectPackageIdApi extends Entity {
    @IntegerField() id: number;
    @StringField() value: string;

    constructor({
        id,
        value
    }: EntityAsSimpleObject<ObjectPackageIdApi>) {
        super();
        this.id = id;
        this.value = value;
    }
}

/**
 * Object API Input class for creating/updating objects in Baserow
 * Based on ObjectEntity structure for Baserow Table ID: 1732
 */
@EntityClass()
export class ObjectApiIn extends Entity {
    /** field_16701: Object code */
    @StringField() code: string;

    /** field_16702: Package ID array - link to table 1731 (packages) */
    @EntityField(ObjectPackageIdApi, {array: true}) packageId: ObjectPackageIdApi[];

    /** field_16703: Object names */
    @StringField() objectNames: string;

    /** field_16704: Return status */
    @BoolField() returned: boolean;

    /** field_16706: Images array (file objects) */
    @StringField({array: true}) images: string[];

    /** field_16707: Return images array (file objects) */
    @StringField({array: true}) returnImages: string[];

    /** field_16708: Comment */
    @StringField() comment: string;

    constructor({
        code,
        packageId,
        objectNames,
        returned,
        images,
        returnImages,
        comment
    }: EntityAsSimpleObject<ObjectApiIn>) {
        super();
        this.code = code;
        this.packageId = packageId;
        this.objectNames = objectNames;
        this.returned = returned;
        this.images = images;
        this.returnImages = returnImages;
        this.comment = comment;
    }
}

/**
 * Object API Output class for responses from Baserow
 * Based on ObjectEntity structure for Baserow Table ID: 1732
 */
@EntityClass()
export class ObjectApiOut extends Entity {
    @IntegerField() id: number;

    /** field_16701: Object code */
    @StringField() code: string;

    /** field_16702: Package ID array - link to table 1731 (packages) */
    @EntityField(ObjectPackageIdApi, {array: true}) packageId: ObjectPackageIdApi[];

    /** field_16703: Object names */
    @StringField({nullable: true}) objectNames: string|null;

    /** field_16704: Return status */
    @BoolField() returned: boolean;

    /** field_16705: Creation date - read-only */
    @DatetimeField() creationDate: string;

    /** field_16706: Images array (file objects) */
    @StringField({array: true}) images: string[];

    /** field_16707: Return images array (file objects) */
    @StringField({array: true}) returnImages: string[];

    /** field_16708: Comment */
    @StringField() comment: string;

    constructor({
        id,
        code,
        packageId,
        objectNames,
        returned,
        creationDate,
        images,
        returnImages,
        comment
    }: EntityAsSimpleObject<ObjectApiOut>) {
        super();
        this.id = id;
        this.code = code;
        this.packageId = packageId;
        this.objectNames = objectNames;
        this.returned = returned;
        this.creationDate = creationDate;
        this.images = images;
        this.returnImages = returnImages;
        this.comment = comment;
    }
}

/**
 * Baserow list response wrapper for Object entities
 */
@EntityClass()
export class ObjectListApiOut extends Entity {
    @IntegerField() count: number;
    @StringField({nullable: true}) next: string | null;
    @StringField({nullable: true}) previous: string | null;
    @EntityField(ObjectApiOut, {array: true}) results: ObjectApiOut[];

    constructor({
        count,
        next,
        previous,
        results
    }: EntityAsSimpleObject<ObjectListApiOut>) {
        super();
        this.count = count;
        this.next = next;
        this.previous = previous;
        this.results = results;
    }
}
