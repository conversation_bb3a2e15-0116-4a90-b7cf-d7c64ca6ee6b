import {
    <PERSON>ol<PERSON>ield,
    DatetimeField,
    Entity,
    EntityClass,
    EntityAsSimpleObject,
    IntegerField,
    StringField
} from "@groupk/horizon2-core";

/**
 * Package entity representing a package in the Baserow database
 * Table ID: 1731
 * 
 * Fields from Baserow API:
 * - field_16693: id (autonumber, primary key, read-only)
 * - field_16695: name (string)
 * - field_16694: creationDate (created_on, read-only)
 * - field_16696: shipping (string)
 * - field_16698: return (string)
 * - field_16699: validated (boolean)
 * - field_16697: objects (array, link to table 1732)
 * - field_16700: comment (string)
 */
@EntityClass()
export class PackageEntity extends Entity {
    /** Package ID - autonumber field, primary key, read-only */
    @IntegerField() id: number;
    
    /** Package name */
    @StringField() name: string;
    
    /** Creation date - read-only field */
    @DatetimeField() creationDate: string;
    
    /** Shipping information */
    @StringField() shipping: string;
    
    /** Return information */
    @StringField() return: string;
    
    /** Validation status */
    @BoolField() validated: boolean;
    
    /** Array of object IDs linked to table 1732 */
    @IntegerField({array: true}) objects: number[];
    
    /** Comment field */
    @StringField() comment: string;

    constructor({
        id,
        name,
        creationDate,
        shipping,
        return: returnValue,
        validated,
        objects,
        comment
    }: EntityAsSimpleObject<PackageEntity>) {
        super();
        this.id = id;
        this.name = name;
        this.creationDate = creationDate;
        this.shipping = shipping;
        this.return = returnValue;
        this.validated = validated;
        this.objects = objects;
        this.comment = comment;
    }
}
