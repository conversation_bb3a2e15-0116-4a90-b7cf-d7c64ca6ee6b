import {BaserowRepository} from './BaserowRepository';
import {ObjectEntity} from '../model/ObjectEntity';

/**
 * Repository for Objects table operations in Baserow
 * Table ID: 1732
 * 
 * Provides CRUD operations for objects with proper entity mapping
 */
export class ObjectRepository extends BaserowRepository {
    protected readonly tableId: number = 1732;

    /**
     * List all objects with optional filtering and pagination
     */
    async listObjects(options: {
        page?: number;
        size?: number;
        search?: string;
        orderBy?: string;
        packageId?: number;
        returned?: boolean;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectEntity[] }> {
        const filters: any = {};
        const filterConditions: any[] = [];
        
        // Add package filter if specified
        if (options.packageId !== undefined) {
            filterConditions.push({
                field: 'field_16702', // packageId field
                type: 'link_row_has',
                value: options.packageId
            });
        }

        // Add return status filter if specified
        if (options.returned !== undefined) {
            filterConditions.push({
                field: 'field_16704', // return field
                type: 'boolean',
                value: options.returned
            });
        }

        if (filterConditions.length > 0) {
            filters.filter_type = 'AND';
            filters.filters = filterConditions;
        }

        const listOptions = {
            ...options,
            userFieldNames: false,
            filters: Object.keys(filters).length > 0 ? filters : undefined
        };

        const response = await this.listRows(listOptions);
        
        return {
            ...response,
            results: response.results.map(this.mapToEntity)
        };
    }

    /**
     * Get a specific object by ID
     */
    async getObject(objectId: number): Promise<ObjectEntity> {
        const response = await this.getRow(objectId, false);
        return this.mapToEntity(response);
    }

    /**
     * Create a new object
     */
    async createObject(objectData: {
        code: string;
        packageId?: number[];
        objectNames?: string;
        return?: boolean;
        images?: any[];
        returnImages?: any[];
        comment?: string;
    }): Promise<ObjectEntity> {
        const data = {
            field_16701: objectData.code,
            field_16702: objectData.packageId || [],
            field_16703: objectData.objectNames || '',
            field_16704: objectData.return || false,
            field_16706: objectData.images || [],
            field_16707: objectData.returnImages || [],
            field_16708: objectData.comment || ''
        };

        const response = await this.createRow(data, false);
        return this.mapToEntity(response);
    }

    /**
     * Update an existing object
     */
    async updateObject(objectId: number, objectData: Partial<{
        code: string;
        packageId: number[];
        objectNames: string;
        return: boolean;
        images: any[];
        returnImages: any[];
        comment: string;
    }>): Promise<ObjectEntity> {
        const data: any = {};
        
        if (objectData.code !== undefined) data.field_16701 = objectData.code;
        if (objectData.packageId !== undefined) data.field_16702 = objectData.packageId;
        if (objectData.objectNames !== undefined) data.field_16703 = objectData.objectNames;
        if (objectData.return !== undefined) data.field_16704 = objectData.return;
        if (objectData.images !== undefined) data.field_16706 = objectData.images;
        if (objectData.returnImages !== undefined) data.field_16707 = objectData.returnImages;
        if (objectData.comment !== undefined) data.field_16708 = objectData.comment;

        const response = await this.updateRow(objectId, data, false);
        return this.mapToEntity(response);
    }

    /**
     * Delete an object
     */
    async deleteObject(objectId: number): Promise<void> {
        await this.deleteRow(objectId);
    }

    /**
     * Search objects by code
     */
    async searchObjectsByCode(searchTerm: string, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectEntity[] }> {
        const filters = {
            filter_type: 'AND',
            filters: [{
                field: 'field_16701', // code field
                type: 'contains',
                value: searchTerm
            }]
        };

        const listOptions = {
            ...options,
            userFieldNames: false,
            filters
        };

        const response = await this.listRows(listOptions);
        
        return {
            ...response,
            results: response.results.map(this.mapToEntity)
        };
    }

    /**
     * Get objects by package ID
     */
    async getObjectsByPackageId(packageId: number, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectEntity[] }> {
        return this.listObjects({
            ...options,
            packageId
        });
    }

    /**
     * Get objects by return status
     */
    async getObjectsByReturnStatus(returned: boolean, options: {
        page?: number;
        size?: number;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: ObjectEntity[] }> {
        return this.listObjects({
            ...options,
            returned
        });
    }

    /**
     * Mark object as returned/not returned
     */
    async setObjectReturnStatus(objectId: number, returned: boolean): Promise<ObjectEntity> {
        return this.updateObject(objectId, { return: returned });
    }

    /**
     * Add images to an object
     */
    async addImagesToObject(objectId: number, images: any[], isReturnImages: boolean = false): Promise<ObjectEntity> {
        const currentObject = await this.getObject(objectId);
        const currentImages = isReturnImages ? currentObject.returnImages : currentObject.images;
        const updatedImages = [...currentImages, ...images];
        
        const updateData = isReturnImages 
            ? { returnImages: updatedImages }
            : { images: updatedImages };
            
        return this.updateObject(objectId, updateData);
    }

    /**
     * Remove images from an object
     */
    async removeImagesFromObject(objectId: number, imageIds: string[], isReturnImages: boolean = false): Promise<ObjectEntity> {
        const currentObject = await this.getObject(objectId);
        const currentImages = isReturnImages ? currentObject.returnImages : currentObject.images;
        const updatedImages = currentImages.filter((img: any) => !imageIds.includes(img.id || img.url));
        
        const updateData = isReturnImages 
            ? { returnImages: updatedImages }
            : { images: updatedImages };
            
        return this.updateObject(objectId, updateData);
    }

    /**
     * Map Baserow API response to ObjectEntity
     */
    private mapToEntity(data: any): ObjectEntity {
        return new ObjectEntity({
            id: data.id,
            code: data.field_16701 || '',
            packageId: data.field_16702 || [],
            objectNames: data.field_16703 || '',
            return: data.field_16704 || false,
            creationDate: data.field_16705 || '',
            images: data.field_16706 || [],
            returnImages: data.field_16707 || [],
            comment: data.field_16708 || ''
        });
    }
}
