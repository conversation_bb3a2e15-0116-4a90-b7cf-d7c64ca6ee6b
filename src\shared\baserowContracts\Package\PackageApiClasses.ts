import {
    <PERSON><PERSON><PERSON><PERSON>,
    DatetimeField,
    Entity,
    EntityClass,
    EntityAsSimpleObject,
    EntityField,
    IntegerField,
    StringField
} from "@groupk/horizon2-core";

/**
 * Package API Input class for creating/updating packages in Baserow
 * Based on PackageEntity structure for Baserow Table ID: 1731
 */
@EntityClass()
export class PackageApiIn extends Entity {
    /** Package name */
    @StringField() name: string;
    
    /** Shipping information */
    @StringField() shipping: string;
    
    /** Return information */
    @StringField() return: string;
    
    /** Validation status */
    @BoolField() validated: boolean;
    
    /** Array of object IDs linked to table 1732 */
    @IntegerField({array: true}) objects: number[];
    
    /** Comment field */
    @StringField() comment: string;

    constructor({
        name,
        shipping,
        return: returnValue,
        validated,
        objects,
        comment
    }: EntityAsSimpleObject<PackageApiIn>) {
        super();
        this.name = name;
        this.shipping = shipping;
        this.return = returnValue;
        this.validated = validated;
        this.objects = objects;
        this.comment = comment;
    }
}

/**
 * Package API Output class for responses from Baserow
 * Based on PackageEntity structure for Baserow Table ID: 1731
 */
@EntityClass()
export class PackageApiOut extends Entity {
    /** Package ID - autonumber field, primary key, read-only */
    @IntegerField() id: number;
    
    /** Package name */
    @StringField() name: string;
    
    /** Creation date - read-only field */
    @DatetimeField() creationDate: string;
    
    /** Shipping information */
    @StringField() shipping: string;
    
    @StringField() returnCode: string;

    /** Validation status */
    @BoolField() validated: boolean;
    
    /** Array of object IDs linked to table 1732 */
    // @IntegerField({array: true}) objects: number[];
    
    /** Comment field */
    @StringField({nullable: true}) comment: string|null;

    constructor({
        id,
        name,
        creationDate,
        shipping,
        returnCode,
        validated,
        comment
    }: EntityAsSimpleObject<PackageApiOut>) {
        super();
        this.id = id;
        this.name = name;
        this.creationDate = creationDate;
        this.shipping = shipping;
        this.returnCode = returnCode;
        this.validated = validated;
        this.comment = comment;
    }
}

/**
 * Baserow list response wrapper for Package entities
 */
@EntityClass()
export class PackageListApiOut extends Entity {
    @IntegerField() count: number;
    @StringField({nullable: true}) next: string | null;
    @StringField({nullable: true}) previous: string | null;
    @EntityField(PackageApiOut, {array: true}) results: PackageApiOut[];

    constructor({
        count,
        next,
        previous,
        results
    }: EntityAsSimpleObject<PackageListApiOut>) {
        super();
        this.count = count;
        this.next = next;
        this.previous = previous;
        this.results = results;
    }
}
