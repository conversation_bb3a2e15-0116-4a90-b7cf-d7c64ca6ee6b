import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, genericFetch} from "@groupk/horizon2-core";
import {FetchError} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import PackageComponent from "./components/PackageComponent/PackageComponent.vue";
import PackageFormComponent from "./components/PackageFormComponent/PackageFormComponent.vue";
import {BarcodeExternalNative, BarcodeExternalNative_QRCodeReaderReturn, SystemInfoNative} from "@groupk/native-bridge";
import {BarcodeScannerManager} from "./BarcodeScannerManager";
import {EventListenerWrapper} from "@groupk/horizon2-core";
import {ObjectNamesEntity, PackageEntity} from "./model";
import {ObjectNamesRepository} from "./repositories/ObjectNamesRepository";

@Component({
    components: {
        'package-form': PackageFormComponent,
        'package': PackageComponent
    }
})
export default class packageScan extends Vue {
    existingPackages: PackageEntity[] = [];
    objectTranslations: ObjectNamesEntity[] = [];

    selectedPackage: PackageEntity|null = null;
    listenerToUnload!: EventListenerWrapper;

    showForm: boolean = false;
    loading: boolean = true;

    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(BarcodeExternalNative) accessor barcodeExternalNative!: BarcodeExternalNative;
    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;
    @AutoWired(ObjectNamesRepository) accessor objectNamesRepository!: ObjectNamesRepository;
    @AutoWired(SystemInfoNative) accessor systemInfoNative!: SystemInfoNative;

    beforeMount() {
        this.sidebarStateListener.setHiddenSidebar(true);
        this.sidebarStateListener.setHiddenTopBar(true);

        this.listenerToUnload = this.barcodeExternalNative.on('allRead', (data) => this.barcodeScannerManager.callback(data));
        this.systemInfoNative.disableBackButton().catch(() => {});

        this.setupListener();
    }

    async mounted() {
        await this.loadTranslations();
        await this.loadPackages()

        this.loading = false;
    }


    unmounted() {
        this.barcodeExternalNative.off('allRead', this.listenerToUnload.listener);
    }

    setupListener() {
        this.barcodeScannerManager.customCallback = this.barCodeRead;
    }

    async loadPackages() {
        this.loading = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/package?maxRecords=80&view=Grid%20view',
            method: 'GET',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515'
            }
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.existingPackages = json.records;

            this.sortPackages();
        }

        this.loading = false;
    }

    async loadTranslations() {
        const response = await this.objectNamesRepository.listObjectNamesWithUserFields();
        this.objectTranslations = response.results.map((rawObject: Record<string, any>) => new ObjectNamesEntity({
            id: rawObject.id,
            code: rawObject.code,
            name: rawObject.name,
            brand: rawObject.brand
        }));
    }

    sortPackages() {
        this.existingPackages.sort((a: PackageEntity, b: PackageEntity) => {
            // Sort by creatingDate desc and validated asc
            if(a.validated === b.validated) {
                return new Date(b.creationDate).getTime() - new Date(a.creationDate).getTime();
            } else {
                return a.validated ? 1 : -1;
            }
        })
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        // Find package base on return then shipping
        const correspondingAirtablePackage = this.existingPackages.find((packageData) => packageData.return === data.content || packageData.shipping === data.content);
        if(correspondingAirtablePackage) {
            this.selectedPackage = correspondingAirtablePackage;
        }
    }

    createdPackage(packageData: PackageEntity) {
        this.existingPackages.push(packageData);

        this.sortPackages();
    }

    updatedPackage(updatedPackageData: PackageEntity) {
        const index = this.existingPackages.findIndex((packageData) => packageData.id === updatedPackageData.id);
        if(index !== -1) this.existingPackages.splice(index, 1, updatedPackageData);

        this.sortPackages();
    }
}