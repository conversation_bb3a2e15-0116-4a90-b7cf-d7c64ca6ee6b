import {
    AbstractHttpDescriptorRepository,
    AbstractHttpDescriptorRepositoryOptions,
    DefaultAbstractHttpDescriptorRepositoryOptions,
    FetchError,
    genericFetch,
    GetInstance,
    HttpRouteDescriptorAggregate
} from '@groupk/horizon2-core';
import {MainConfig} from "../../../../../../shared/MainConfig";

/**
 * Base repository for Baserow API interactions
 * Provides common functionality for all Baserow table repositories
 */
export abstract class BaserowRepository {
    protected readonly baseUrl: string = 'https://baserow-api.lab.weecop.fr';
    protected readonly databaseId: number = 446;
    protected readonly authToken: string = 'fCK4Uh9UE8bo8KKRM8dHPvReYiQuf7wL';
    
    protected abstract readonly tableId: number;

    /**
     * Get the base URL for table operations
     */
    protected getTableUrl(): string {
        return `${this.baseUrl}/api/database/rows/table/${this.tableId}/`;
    }

    /**
     * Get the fields URL for table schema
     */
    protected getFieldsUrl(): string {
        return `${this.baseUrl}/api/database/fields/table/${this.tableId}/`;
    }

    /**
     * Get common headers for API requests
     */
    protected getHeaders(): Record<string, string> {
        return {
            'Authorization': `Token ${this.authToken}`,
            'Content-Type': 'application/json'
        };
    }

    /**
     * List all fields for the table
     */
    async listFields(): Promise<any[]> {
        const response = await genericFetch({
            url: this.getFieldsUrl(),
            method: 'GET',
            headers: this.getHeaders()
        });

        if (response instanceof FetchError) {
            throw new Error(`Failed to fetch fields: ${response.message}`);
        }

        return await response.json();
    }

    /**
     * List rows with optional pagination and filtering
     */
    async listRows(options: {
        page?: number;
        size?: number;
        userFieldNames?: boolean;
        search?: string;
        orderBy?: string;
        filters?: any;
    } = {}): Promise<{ count: number; next: string | null; previous: string | null; results: any[] }> {
        const params = new URLSearchParams();
        
        if (options.page) params.append('page', options.page.toString());
        if (options.size) params.append('size', options.size.toString());
        if (options.userFieldNames) params.append('user_field_names', 'true');
        if (options.search) params.append('search', options.search);
        if (options.orderBy) params.append('order_by', options.orderBy);
        if (options.filters) params.append('filters', JSON.stringify(options.filters));

        const url = `${this.getTableUrl()}?${params.toString()}`;
        
        const response = await genericFetch({
            url,
            method: 'GET',
            headers: this.getHeaders()
        });

        if (response instanceof FetchError) {
            throw new Error(`Failed to fetch rows: ${response.message}`);
        }

        return await response.json();
    }

    /**
     * Get a specific row by ID
     */
    async getRow(rowId: number, userFieldNames: boolean = false): Promise<any> {
        const params = userFieldNames ? '?user_field_names=true' : '';
        const url = `${this.getTableUrl()}${rowId}/${params}`;
        
        const response = await genericFetch({
            url,
            method: 'GET',
            headers: this.getHeaders()
        });

        if (response instanceof FetchError) {
            throw new Error(`Failed to fetch row ${rowId}: ${response.message}`);
        }

        return await response.json();
    }

    /**
     * Create a new row
     */
    async createRow(data: any, userFieldNames: boolean = false): Promise<any> {
        const params = userFieldNames ? '?user_field_names=true' : '';
        const url = `${this.getTableUrl()}${params}`;
        
        const response = await genericFetch({
            url,
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify(data)
        });

        if (response instanceof FetchError) {
            throw new Error(`Failed to create row: ${response.message}`);
        }

        return await response.json();
    }

    /**
     * Update an existing row
     */
    async updateRow(rowId: number, data: any, userFieldNames: boolean = false): Promise<any> {
        const params = userFieldNames ? '?user_field_names=true' : '';
        const url = `${this.getTableUrl()}${rowId}/${params}`;
        
        const response = await genericFetch({
            url,
            method: 'PATCH',
            headers: this.getHeaders(),
            body: JSON.stringify(data)
        });

        if (response instanceof FetchError) {
            throw new Error(`Failed to update row ${rowId}: ${response.message}`);
        }

        return await response.json();
    }

    /**
     * Move a row to a different position
     */
    async moveRow(rowId: number, beforeRowId?: number, userFieldNames: boolean = false): Promise<any> {
        const params = userFieldNames ? '?user_field_names=true' : '';
        const url = `${this.getTableUrl()}${rowId}/move/${params}`;
        
        const body: any = {};
        if (beforeRowId !== undefined) {
            body.before_id = beforeRowId;
        }
        
        const response = await genericFetch({
            url,
            method: 'PATCH',
            headers: this.getHeaders(),
            body: JSON.stringify(body)
        });

        if (response instanceof FetchError) {
            throw new Error(`Failed to move row ${rowId}: ${response.message}`);
        }

        return await response.json();
    }

    /**
     * Delete a row
     */
    async deleteRow(rowId: number): Promise<void> {
        const url = `${this.getTableUrl()}${rowId}/`;
        
        const response = await genericFetch({
            url,
            method: 'DELETE',
            headers: this.getHeaders()
        });

        if (response instanceof FetchError) {
            throw new Error(`Failed to delete row ${rowId}: ${response.message}`);
        }
    }
}
